import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  MessageCircle,
  ArrowUp,
  Plus,
  X,
  Zap
} from 'lucide-react'
import { scrollToTopSmooth } from '@/components/ScrollToTop'
import ChatAssistant from '@/components/ChatAssistant'

interface SpeedDialAction {
  icon: React.ElementType
  label: string
  action: () => void
  color: string
  show: boolean
}

/**
 * Advanced Speed Dial FAB Component
 * Combines multiple floating actions into an elegant, expandable menu
 */
export const SpeedDialFAB = () => {
  const [isOpen, setIsOpen] = useState(false)
  const [showScrollToTop, setShowScrollToTop] = useState(false)
  const [isChatOpen, setIsChatOpen] = useState(false)

  // Keyboard accessibility
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        setIsOpen(false)
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [isOpen])

  // Monitor scroll position to show/hide scroll-to-top action
  useEffect(() => {
    const toggleScrollVisibility = () => {
      setShowScrollToTop(window.pageYOffset > 300)
    }

    window.addEventListener('scroll', toggleScrollVisibility)
    return () => window.removeEventListener('scroll', toggleScrollVisibility)
  }, [])

  // Handle scroll to top
  const handleScrollToTop = () => {
    setIsOpen(false)

    // Haptic feedback (if supported)
    if ('vibrate' in navigator) {
      navigator.vibrate(50)
    }

    scrollToTopSmooth()
  }

  // Handle chat assistant - opens the actual chat modal
  const handleChatAssistant = () => {
    setIsOpen(false)
    setIsChatOpen(true)

    // Haptic feedback
    if ('vibrate' in navigator) {
      navigator.vibrate(50)
    }
  }

  // Define speed dial actions
  const actions: SpeedDialAction[] = [
    {
      icon: ArrowUp,
      label: 'Scroll to Top',
      action: handleScrollToTop,
      color: 'bg-blue-500 hover:bg-blue-600',
      show: showScrollToTop
    },
    {
      icon: MessageCircle,
      label: 'Chat Assistant',
      action: handleChatAssistant,
      color: 'bg-green-500 hover:bg-green-600',
      show: true
    }
  ]

  // Filter visible actions
  const visibleActions = actions.filter(action => action.show)

  // Handle main button toggle with feedback
  const handleToggle = () => {
    // Haptic feedback
    if ('vibrate' in navigator) {
      navigator.vibrate(30)
    }

    setIsOpen(!isOpen)
  }

  // Animation variants
  const containerVariants = {
    open: {
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.1
      }
    },
    closed: {
      transition: {
        staggerChildren: 0.05,
        staggerDirection: -1
      }
    }
  }

  const itemVariants = {
    open: {
      y: 0,
      opacity: 1,
      scale: 1,
      transition: {
        type: "spring" as const,
        stiffness: 300,
        damping: 24
      }
    },
    closed: {
      y: 20,
      opacity: 0,
      scale: 0.8,
      transition: {
        duration: 0.2
      }
    }
  }

  const mainButtonVariants = {
    open: { rotate: 0, scale: 1.1 },
    closed: { rotate: 0, scale: 1 }
  }

  return (
    <>
      <div className="fixed bottom-6 right-6 z-50">
      {/* Speed Dial Actions */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            variants={containerVariants}
            initial="closed"
            animate="open"
            exit="closed"
            className="absolute bottom-16 right-0 flex flex-col-reverse gap-4"
            style={{ 
              alignItems: 'flex-end', // Align items to the right edge
              transform: 'translateX(0)' // Ensure no horizontal offset
            }}
          >
            {visibleActions.map((action, index) => (
              <motion.div
                key={action.label}
                variants={itemVariants}
                className="flex items-center gap-3 justify-end"
              >
                {/* Action Label */}
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  transition={{ delay: index * 0.1 }}
                  className="bg-gray-900/95 text-white px-3 py-1.5 rounded-lg text-xs font-medium whitespace-nowrap backdrop-blur-sm border border-gray-700/50 shadow-lg"
                >
                  {action.label}
                </motion.div>

                {/* Action Button - Now perfectly aligned with main button */}
                <motion.button
                  onClick={action.action}
                  whileHover={{ scale: 1.1, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  className={`${action.color} text-white p-3 rounded-full shadow-xl border border-white/20 backdrop-blur-sm transition-all duration-300 group w-12 h-12 flex items-center justify-center`}
                >
                  <action.icon className="w-4 h-4 group-hover:scale-110 transition-transform duration-200" />
                </motion.button>
              </motion.div>
            ))}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main FAB Button */}
      <motion.button
        onClick={handleToggle}
        variants={mainButtonVariants}
        animate={isOpen ? "open" : "closed"}
        whileHover={{ scale: 1.05, y: -2 }}
        whileTap={{ scale: 0.95 }}
        className="bg-primary hover:bg-primary/90 text-primary-foreground p-3 rounded-full shadow-2xl border border-primary/20 backdrop-blur-sm transition-all duration-300 group relative overflow-hidden w-12 h-12 flex items-center justify-center"
        aria-label={isOpen ? "Close menu" : "Open actions menu"}
      >
        {/* Background gradient animation */}
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-violet-600 via-purple-600 to-violet-700"
          animate={{
            background: isOpen 
              ? "linear-gradient(45deg, #7c3aed, #a855f7, #7c3aed)"
              : "linear-gradient(90deg, #7c3aed, #a855f7, #7c3aed)"
          }}
          transition={{ duration: 0.3 }}
        />

        {/* Icon with smooth transition */}
        <motion.div className="relative z-10">
          <AnimatePresence mode="wait">
            {isOpen ? (
              <motion.div
                key="close"
                initial={{ rotate: -90, opacity: 0 }}
                animate={{ rotate: 0, opacity: 1 }}
                exit={{ rotate: 90, opacity: 0 }}
                transition={{ duration: 0.2 }}
              >
                <X className="w-5 h-5" />
              </motion.div>
            ) : (
              <motion.div
                key="open"
                initial={{ rotate: 90, opacity: 0 }}
                animate={{ rotate: 0, opacity: 1 }}
                exit={{ rotate: -90, opacity: 0 }}
                transition={{ duration: 0.2 }}
              >
                <Zap className="w-5 h-5" />
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>

        {/* Pulse effect when closed */}
        {!isOpen && (
          <motion.div
            className="absolute inset-0 rounded-full bg-primary/30"
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.5, 0, 0.5]
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
        )}
      </motion.button>

      {/* Backdrop overlay */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setIsOpen(false)}
            className="fixed inset-0 bg-black/20 backdrop-blur-sm -z-10"
          />
        )}
      </AnimatePresence>
      </div>

      {/* Chat Assistant Modal */}
      <AnimatePresence>
        {isChatOpen && (
          <ChatAssistant onClose={() => setIsChatOpen(false)} />
        )}
      </AnimatePresence>
    </>
  )
}

export default SpeedDialFAB