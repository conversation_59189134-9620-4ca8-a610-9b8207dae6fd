import { useState, useEffect } from "react"
import { useLocation, useNavigate } from "react-router-dom"
import { Home, User, Code2, FolderOpen, MessageSquare, Menu, X } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { scrollToTopInstant } from "@/components/ScrollToTop"
import { cn } from "@/lib/utils"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"

const navItems = [
  { path: "/", icon: Home, label: "Dashboard" },
  { path: "/about", icon: User, label: "About" },
  { path: "/skills", icon: Code2, label: "Skills" },
  { path: "/projects", icon: FolderOpen, label: "Projects" },
  { path: "/contact", icon: MessageSquare, label: "Contact" },
]

export function FloatingNav() {
  const location = useLocation()
  const navigate = useNavigate()
  const [isOpen, setIsOpen] = useState(false)

  // Close mobile menu when route changes
  useEffect(() => {
    setIsOpen(false)
  }, [location.pathname])

  // Enhanced navigation function that scrolls to top before navigating
  const handleNavigation = (path: string) => {
    scrollToTopInstant()
    navigate(path)
  }

  return (
    <>
      {/* Desktop Floating Navigation */}
      <div className="fixed right-6 top-1/2 -translate-y-1/2 z-50 hidden lg:block">
        <TooltipProvider>
          <div className="flex flex-col gap-2 bg-background/90 backdrop-blur-xl border border-border/30 rounded-xl p-3 shadow-lg">
            {navItems.map(({ path, icon: Icon, label }) => (
              <Tooltip key={path}>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className={cn(
                      "w-10 h-10 p-0 rounded-lg transition-all duration-200 relative group",
                      location.pathname === path
                        ? "bg-primary/10 text-primary shadow-sm"
                        : "hover:bg-muted/50 hover:text-foreground text-muted-foreground/70"
                    )}
                    onClick={() => handleNavigation(path)}
                  >
                    <Icon className="w-4 h-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="left" className="text-xs font-medium bg-background/95 backdrop-blur-sm border border-border/50">
                  <p>{label}</p>
                </TooltipContent>
              </Tooltip>
            ))}

          </div>
        </TooltipProvider>
      </div>

      {/* Mobile Navigation */}
      <div className="lg:hidden">
        {/* Mobile Menu Button */}
        <Button
          variant="default"
          size="sm"
          className="fixed top-4 right-4 z-50 w-11 h-11 p-0 rounded-xl shadow-lg bg-background/90 backdrop-blur-sm border border-border/50 text-foreground hover:bg-muted/50"
          onClick={() => setIsOpen(!isOpen)}
        >
          {isOpen ? <X className="w-4 h-4" /> : <Menu className="w-4 h-4" />}
        </Button>

        {/* Mobile Menu Overlay */}
        {isOpen && (
          <div className="fixed inset-0 z-40 bg-background/95 backdrop-blur-md">
            <div className="flex h-full items-center justify-center">
              <div className="grid gap-4 text-center">
                {navItems.map(({ path, icon: Icon, label }) => (
                  <Button
                    key={path}
                    variant={location.pathname === path ? "default" : "ghost"}
                    size="lg"
                    className={cn(
                      "w-44 justify-start gap-3 h-12",
                      location.pathname === path
                        ? "bg-primary/10 text-primary border border-primary/20"
                        : "hover:bg-muted/50"
                    )}
                    onClick={() => handleNavigation(path)}
                  >
                    <Icon className="w-4 h-4" />
                    <span className="font-medium">{label}</span>
                  </Button>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Mobile Bottom Navigation */}
        <div className="fixed bottom-0 left-0 right-0 z-30 border-t border-border/30 bg-background/95 backdrop-blur-md supports-[backdrop-filter]:bg-background/80 lg:hidden">
          <div className="flex items-center justify-around px-2 py-3">
            {navItems.map(({ path, icon: Icon, label }) => (
              <Button
                key={path}
                variant="ghost"
                size="sm"
                className={cn(
                  "flex-col gap-1 h-12 w-12 p-1 rounded-lg",
                  location.pathname === path 
                    ? "text-primary bg-primary/5" 
                    : "text-muted-foreground/70 hover:text-foreground hover:bg-muted/30"
                )}
                onClick={() => handleNavigation(path)}
              >
                <Icon className="w-4 h-4" />
                <span className="text-xs font-medium">{label}</span>
              </Button>
            ))}
          </div>
        </div>
      </div>
    </>
  )
}