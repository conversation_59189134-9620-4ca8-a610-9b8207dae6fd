import React from 'react'
import { motion } from 'framer-motion'
import { useNavigate } from 'react-router-dom'
import Section from '@/components/Section'
import { scrollToTopInstant } from '@/components/ScrollToTop'
import { OutlineButton } from '@/components/ui/enhanced-button'
import { BaseSectionProps, staggerContainerVariants, itemVariants } from './types'
import { renderIcon, createNavigationHandler } from './utils'
import { coreSkills } from './data'

export default function CoreTechnologiesSection({ className }: BaseSectionProps) {
  const navigate = useNavigate()
  const handleNavigation = createNavigationHandler(navigate, scrollToTopInstant)

  return (
    <Section 
      title="Core Technologies"
      subtitle="The tools and technologies I use to bring ideas to life."
      className={className}
    >
      <motion.div
        className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6"
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
        variants={staggerContainerVariants}
      >
        {coreSkills.map((skill) => (
          <motion.div
            key={skill.name}
            variants={itemVariants}
            whileHover={{ y: -1 }}
            className="group flex flex-col items-center space-y-3 p-4 rounded-xl hover:bg-primary/3 dark:hover:bg-primary/5 transition-all duration-200"
            aria-label={`${skill.name} - ${skill.category} technology`}
          >
            <motion.div
              className="text-3xl"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.2 }}
            >
              {renderIcon(skill.icon, "w-8 h-8")}
            </motion.div>
            <div className="text-center">
              <p className="font-semibold text-sm group-hover:text-primary transition-colors duration-300">
                {skill.name}
              </p>
              <p className="text-sm text-muted-foreground mt-1 font-medium">
                {skill.category}
              </p>
            </div>
          </motion.div>
        ))}
      </motion.div>

      <div className="text-center mt-12 pt-8 border-t border-border/50">
        <OutlineButton
          onClick={() => handleNavigation('/skills')}
        >
          Explore My Full Skillset
        </OutlineButton>
      </div>
    </Section>
  )
}
