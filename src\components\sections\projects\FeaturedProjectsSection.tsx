import React from "react"
import { motion } from "framer-motion"
import { ExternalLink, Github, ShoppingCart, CheckSquare, Users, Star } from "lucide-react"
import { PrimaryButton, OutlineButton } from "@/components/ui/enhanced-button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from "@/components/ui/carousel"
import DeviceMockup from "@/components/DeviceMockup"
import Section from "@/components/Section"
import { BaseSectionProps, staggerContainerVariants, itemVariants } from "./types"
import { renderIcon, techIcons, getStatusColor } from "./utils"
import { projects } from "@/data/projects/projectsData"
import { cn } from "@/lib/utils"

export function FeaturedProjectsSection({ className }: BaseSectionProps) {
  const featuredProjects = projects.filter(project => project.featured)

  return (
    <Section
      title="Featured Work"
      subtitle="Highlighting my most impactful projects that demonstrate technical expertise and creative problem-solving."
      className={className}
    >
      <motion.div
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
        variants={staggerContainerVariants}
      >
        <Carousel className="w-full max-w-5xl mx-auto">
          <CarouselContent className="-ml-2 md:-ml-4">
            {featuredProjects.map((project, index) => (
              <CarouselItem key={project.id} className="pl-2 md:pl-4 md:basis-1/2 lg:basis-1/2">
                <motion.div
                  variants={itemVariants}
                  whileHover={{ y: -4, scale: 1.02 }}
                  transition={{ duration: 0.3 }}
                  className="group h-full"
                >
                  <Card className="h-full hover:shadow-soft-xl transition-all duration-300 border-border/50 bg-card/90 backdrop-blur-sm hover:border-primary/20 overflow-hidden">
                    {/* Project Preview */}
                    <div className="aspect-video bg-gradient-to-br from-primary/5 to-primary/10 dark:from-primary/10 dark:to-primary/20 relative overflow-hidden">
                      <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                      <div className="w-full h-full flex items-center justify-center p-4">
                        <DeviceMockup
                          type={project.deviceType}
                          className="max-h-full group-hover:scale-[1.02] transition-transform duration-300"
                        >
                          {project.id === 1 && (
                            <div className="p-3 sm:p-4 space-y-2 sm:space-y-3 bg-white">
                              {/* E-commerce mockup */}
                              <div className="flex items-center justify-between">
                                <div className="h-5 sm:h-6 bg-purple-600 rounded px-2 flex items-center">
                                  <ShoppingCart className="w-2.5 sm:w-3 h-2.5 sm:h-3 text-white mr-1" />
                                  <span className="text-xs text-white font-medium">ShopHub</span>
                                </div>
                                <div className="flex gap-1">
                                  <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                                  <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                </div>
                              </div>
                              <div className="grid grid-cols-3 gap-1.5 sm:gap-2">
                                {[1, 2, 3, 4, 5, 6].map((item) => (
                                  <div key={item} className="aspect-square bg-gray-100 rounded flex items-center justify-center">
                                    <div className="w-3 sm:w-4 h-3 sm:h-4 bg-purple-200 rounded"></div>
                                  </div>
                                ))}
                              </div>
                              <div className="flex justify-between items-center">
                                <div className="h-3 sm:h-4 bg-gray-200 rounded flex-1 mr-2"></div>
                                <div className="h-6 sm:h-8 bg-purple-600 rounded px-2 flex items-center">
                                  <span className="text-xs text-white">Cart</span>
                                </div>
                              </div>
                            </div>
                          )}
                          {project.id === 2 && (
                            <div className="p-2.5 sm:p-3 space-y-1.5 sm:space-y-2 bg-white">
                              {/* Task app mockup */}
                              <div className="flex items-center justify-between bg-slate-100 rounded p-1.5 sm:p-2">
                                <div className="flex items-center gap-1">
                                  <CheckSquare className="w-2.5 sm:w-3 h-2.5 sm:h-3 text-purple-600" />
                                  <span className="text-xs font-medium">TaskFlow</span>
                                </div>
                                <div className="flex gap-0.5">
                                  <div className="w-1.5 h-1.5 bg-red-500 rounded-full"></div>
                                  <div className="w-1.5 h-1.5 bg-yellow-500 rounded-full"></div>
                                  <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                                </div>
                              </div>
                              <div className="space-y-1">
                                {[1, 2, 3, 4].map((task) => (
                                  <div key={task} className="flex items-center gap-1.5 p-1 bg-gray-50 rounded">
                                    <div className="w-2 h-2 border border-gray-300 rounded-sm"></div>
                                    <div className="h-2 bg-gray-200 rounded flex-1"></div>
                                    <div className="w-3 h-3 bg-purple-100 rounded-full flex items-center justify-center">
                                      <Users className="w-1.5 h-1.5 text-purple-600" />
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                          {project.id === 3 && (
                            <div className="p-3 sm:p-4 space-y-2 sm:space-y-3 bg-white">
                              {/* Portfolio mockup */}
                              <div className="flex items-center justify-between">
                                <div className="flex items-center gap-1.5 sm:gap-2">
                                  <div className="w-3 sm:w-4 h-3 sm:h-4 bg-gradient-to-r from-purple-500 to-violet-600 rounded"></div>
                                  <span className="text-xs font-bold text-gray-800">Chris.dev</span>
                                </div>
                                <div className="flex gap-1">
                                  <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                                  <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                </div>
                              </div>
                              <div className="text-center py-2 sm:py-3">
                                <div className="w-8 sm:w-10 h-8 sm:h-10 bg-gradient-to-br from-purple-400 to-violet-600 rounded-full mx-auto mb-1 sm:mb-2"></div>
                                <div className="h-2 sm:h-3 bg-gray-800 rounded mx-auto mb-1" style={{ width: '60%' }}></div>
                                <div className="h-1.5 sm:h-2 bg-gray-400 rounded mx-auto" style={{ width: '80%' }}></div>
                              </div>
                              <div className="grid grid-cols-3 gap-1 sm:gap-1.5">
                                {[1, 2, 3].map((item) => (
                                  <div key={item} className="aspect-square bg-gradient-to-br from-purple-100 to-violet-100 rounded flex items-center justify-center">
                                    <div className="w-2 sm:w-3 h-2 sm:h-3 bg-purple-300 rounded"></div>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                        </DeviceMockup>
                      </div>
                    </div>

                    <CardHeader className="pb-4">
                      <div className="flex items-start justify-between">
                        <div className="space-y-2">
                          <div className="flex items-center gap-2">
                            <CardTitle className="text-xl font-bold group-hover:text-primary transition-colors duration-300">
                              {project.title}
                            </CardTitle>
                            {project.featured && (
                              <Star className="w-4 h-4 text-yellow-500 fill-current" />
                            )}
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge className={cn("text-xs font-medium", getStatusColor(project.status))}>
                              {project.status}
                            </Badge>
                            <Badge variant="outline" className="text-xs">
                              {project.type}
                            </Badge>
                          </div>
                        </div>
                      </div>
                      <CardDescription className="text-sm leading-relaxed">
                        {project.description}
                      </CardDescription>
                    </CardHeader>

                    <CardContent className="space-y-4">
                      {/* Technologies */}
                      <div className="flex flex-wrap gap-1.5">
                        {project.technologies.slice(0, 4).map((tech) => {
                          const TechIcon = techIcons[tech]
                          return (
                            <Badge
                              key={tech}
                              variant="secondary"
                              className="text-xs font-medium bg-primary/10 text-primary border-primary/20 flex items-center gap-1"
                            >
                              {TechIcon && renderIcon(TechIcon, "w-3 h-3")}
                              {tech}
                            </Badge>
                          )
                        })}
                        {project.technologies.length > 4 && (
                          <Badge variant="outline" className="text-xs">
                            +{project.technologies.length - 4}
                          </Badge>
                        )}
                      </div>

                      {/* Action Buttons */}
                      <div className="flex gap-2 pt-2">
                        <PrimaryButton
                          onClick={() => window.open(project.liveUrl, '_blank')}
                          className="flex-1 text-xs px-3 py-1.5"
                          disabled={project.liveUrl === "#"}
                          arrowAnimation="slide"
                        >
                          <ExternalLink className="w-3 h-3 mr-1" />
                          Live Demo
                        </PrimaryButton>
                        <OutlineButton
                          onClick={() => window.open(project.githubUrl, '_blank')}
                          className="flex-1 text-xs px-3 py-1.5"
                          arrowAnimation="fade"
                        >
                          <Github className="w-3 h-3 mr-1" />
                          Code
                        </OutlineButton>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              </CarouselItem>
            ))}
          </CarouselContent>
          <CarouselPrevious className="hidden sm:flex -left-2 md:-left-4 h-8 w-8 sm:h-10 sm:w-10 border-2 border-border/50 bg-background/80 dark:bg-slate-900/90 backdrop-blur-sm hover:bg-background dark:hover:bg-slate-900 hover:border-primary/50 transition-all duration-200" />
          <CarouselNext className="hidden sm:flex -right-2 md:-right-4 h-8 w-8 sm:h-10 sm:w-10 border-2 border-border/50 bg-background/80 dark:bg-slate-900/90 backdrop-blur-sm hover:bg-background dark:hover:bg-slate-900 hover:border-primary/50 transition-all duration-200" />
        </Carousel>
      </motion.div>
    </Section>
  )
}
