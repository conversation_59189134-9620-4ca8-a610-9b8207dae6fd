// Components
export { HeroSection } from "./HeroSection"
export { FeaturedProjectsSection } from "./FeaturedProjectsSection"
export { ProjectsGridSection } from "./ProjectsGridSection"
export { CallToActionSection } from "./CallToActionSection"

// Types
export type { BaseSectionProps, Project, FilterCategory, TechIcon } from "./types"
export { containerVariants, itemVariants, staggerContainerVariants } from "./types"

// Utils
export { 
  renderIcon, 
  createNavigationHandler, 
  getProjectsByCategory, 
  getFilterCategories, 
  getDeviceIcon, 
  getStatusColor,
  techIcons 
} from "./utils"

// Data
export { projects } from "./data"
