import React from "react"
import { motion } from "framer-motion"
import { PrimaryButton, OutlineButton } from "@/components/ui/enhanced-button"
import { BaseSectionProps, containerVariants, itemVariants } from "./types"
import { createNavigationHandler } from "./utils"
import { projects } from "./data"

export function HeroSection({ className }: BaseSectionProps) {
  const handleNavigation = createNavigationHandler()

  return (
    <motion.div
      className={`mb-24 md:mb-32 ${className || ""}`}
      initial="hidden"
      animate="visible"
      variants={containerVariants}
    >
      <div className="text-center space-y-8">
        <motion.div
          className="space-y-6"
          variants={itemVariants}
        >
          <motion.h1
            className="text-4xl md:text-5xl lg:text-6xl font-extrabold text-foreground leading-tight tracking-tight"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, delay: 0.05 }}
          >
            My{' '}
            <span className="text-primary bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">
              Projects
            </span>
          </motion.h1>

          <motion.p
            className="text-lg md:text-xl text-muted-foreground leading-relaxed max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, delay: 0.1 }}
          >
            A showcase of my development journey through real-world applications. 
            Each project represents a unique challenge solved with modern technologies and creative problem-solving.
          </motion.p>
        </motion.div>

        <motion.div
          className="flex flex-col sm:flex-row gap-4 justify-center items-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.15 }}
        >
          <PrimaryButton
            onClick={() => handleNavigation('/contact')}
            className="px-8 py-4"
            arrowAnimation="slide"
          >
            Start a Project
          </PrimaryButton>
          <OutlineButton
            onClick={() => handleNavigation('/skills')}
            className="px-8 py-4"
            arrowAnimation="fade"
          >
            View My Skills
          </OutlineButton>
        </motion.div>

        {/* Project Stats */}
        <motion.div
          className="grid grid-cols-2 md:grid-cols-4 gap-6 pt-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.2 }}
        >
          <div className="text-center space-y-2">
            <div className="text-2xl md:text-3xl font-bold text-primary">{projects.length}+</div>
            <div className="text-sm text-muted-foreground">Projects Built</div>
          </div>
          <div className="text-center space-y-2">
            <div className="text-2xl md:text-3xl font-bold text-primary">
              {projects.filter(p => p.category === "Full-Stack").length}
            </div>
            <div className="text-sm text-muted-foreground">Full-Stack Apps</div>
          </div>
          <div className="text-center space-y-2">
            <div className="text-2xl md:text-3xl font-bold text-primary">
              {projects.filter(p => p.status === "Completed").length}
            </div>
            <div className="text-sm text-muted-foreground">Completed</div>
          </div>
          <div className="text-center space-y-2">
            <div className="text-2xl md:text-3xl font-bold text-primary">2+</div>
            <div className="text-sm text-muted-foreground">Years Experience</div>
          </div>
        </motion.div>
      </div>
    </motion.div>
  )
}
