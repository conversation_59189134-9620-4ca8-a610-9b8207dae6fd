import React from 'react'
import { motion } from 'framer-motion'
import { Star, ExternalLink } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { OutlineButton } from '@/components/ui/enhanced-button'
import Section from '@/components/Section'
import { BaseSectionProps, staggerContainerVariants, itemVariants } from './types'
import { learningResources } from './data'

export default function LearningResourcesSection({ className }: BaseSectionProps) {
  return (
    <Section
      title="My Digital Bookshelf"
      subtitle="The resources that have shaped my understanding of development and continue to inspire my growth."
      className={className}
    >
      <motion.div
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
        variants={staggerContainerVariants}
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {learningResources.map((resource) => (
            <motion.div key={resource.title} variants={itemVariants}>
              <Card className="group h-full hover:shadow-lg transition-all duration-300 border-border/50 bg-card/90 backdrop-blur-sm transform hover:scale-[1.02] cursor-pointer">
                <CardHeader className="pb-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <CardTitle className="text-lg font-bold group-hover:text-primary transition-colors duration-300">
                        {resource.title}
                      </CardTitle>
                      <CardDescription className="mt-1">
                        by {resource.author}
                      </CardDescription>
                    </div>
                    <Badge variant="secondary" className="bg-primary/10 text-primary border-primary/20">
                      {resource.type}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-start gap-2">
                    <Star className="w-4 h-4 text-primary flex-shrink-0 mt-0.5" />
                    <p className="text-sm text-muted-foreground italic">
                      "{resource.takeaway}"
                    </p>
                  </div>
                  <OutlineButton
                    className="w-full text-sm px-4 py-2"
                    onClick={() => window.open(resource.link, '_blank')}
                    showArrow={false}
                  >
                    <ExternalLink className="w-3 h-3 mr-2" />
                    View Resource
                  </OutlineButton>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </motion.div>
    </Section>
  )
}
