import React from 'react'
import { Folder, <PERSON>Open } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'
import Section from '@/components/Section'
import { BaseSectionProps } from './types'
import { renderIcon } from './utils'
import { currentlyLearning, workingOn } from './data'

export default function CurrentFocusSection({ className }: BaseSectionProps) {
  return (
    <Section 
      title="Current Focus"
      subtitle="What I'm building and learning right now to stay at the forefront of web development."
      className={className}
    >
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* What I'm Building - More structured layout */}
        <Card className="group hover:shadow-sm transition-all duration-200 border-border/50 bg-card/90 backdrop-blur-sm">
          <CardHeader className="pb-6">
            <CardTitle className="flex items-center gap-3 text-xl font-bold">
              <div className="w-10 h-10 bg-gradient-to-br from-primary/20 to-primary/10 dark:from-primary/30 dark:to-primary/20 rounded-xl flex items-center justify-center">
                <Folder className="w-5 h-5 text-primary" />
              </div>
              What I'm Building
            </CardTitle>
            <CardDescription className="text-muted-foreground">
              Current projects and ongoing development
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {workingOn.map((project) => (
                <div key={project.name} className="flex items-center justify-between p-4 rounded-xl hover:bg-muted/20 transition-colors duration-200 group/item border border-border/30">
                  <div className="flex items-center gap-3">
                    {renderIcon(project.icon, cn(
                      "w-4 h-4 transition-colors duration-200",
                      project.status === "Active" ? "text-green-600" : "text-purple-600"
                    ))}
                    <span className="font-medium text-muted-foreground group-hover/item:text-foreground transition-colors duration-200">
                      {project.name}
                    </span>
                  </div>
                  <Badge
                    variant={project.status === "Active" ? "default" : "secondary"}
                    className={cn(
                      "text-xs px-3 py-1 font-medium",
                      project.status === "Active"
                        ? "bg-green-500/10 text-green-600 border-green-500/20"
                        : "bg-purple-500/10 text-purple-600 border-purple-500/20"
                    )}
                  >
                    {project.status}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* What I'm Learning - Cleaner list style */}
        <Card className="group hover:shadow-sm transition-all duration-200 border-border/50 bg-card/90 backdrop-blur-sm">
          <CardHeader className="pb-6">
            <CardTitle className="flex items-center gap-3 text-xl font-bold">
              <div className="w-10 h-10 bg-gradient-to-br from-primary/20 to-primary/10 dark:from-primary/30 dark:to-primary/20 rounded-xl flex items-center justify-center">
                <BookOpen className="w-5 h-5 text-primary" />
              </div>
              What I'm Learning
            </CardTitle>
            <CardDescription className="text-muted-foreground">
              Technologies and concepts I'm actively studying
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {currentlyLearning.map((item) => (
                <div key={item.name} className="flex items-center gap-4 p-3 rounded-lg hover:bg-muted/20 transition-colors duration-200 group/item">
                  {renderIcon(item.icon, "w-4 h-4 text-primary/70 animate-pulse")}
                  <span className="font-medium text-muted-foreground group-hover/item:text-foreground transition-colors duration-200">
                    {item.name}
                  </span>
                </div>
              ))}
            </div>
            <div className="mt-6 pt-4 border-t border-border/50">
              <p className="text-sm text-muted-foreground">
                Continuously expanding my skillset to stay current with modern web development practices.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </Section>
  )
}
