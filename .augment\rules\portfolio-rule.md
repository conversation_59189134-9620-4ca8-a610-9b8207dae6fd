---
type: "manual"
---

Project Rules & Development Guidelines
This section serves as the definitive "source of truth" for maintaining the project's quality, architecture, and style.

1. Core Philosophy

Excellence & Ownership: Every line of code, component, and design choice must reflect the highest standards of quality. Take ownership of your work and be proactive in improving the codebase.

Clarity Over Cleverness: Code must be self-documenting, readable, and maintainable. Prioritize straightforward solutions.

Consistency is Paramount: Strictly adhere to the defined folder structure, naming conventions, and coding standards to ensure a predictable and cohesive project.

2. Technology & Tools

Application Stack:

Frontend: React 18+, TypeScript, Vite, Tailwind CSS

UI Components: Shadcn UI (for base elements), custom components for unique features.

Animations: Framer Motion

Backend: Node.js w/ Express.js (Future consideration: NestJS)

Database: PostgreSQL (managed via Supabase)

ORM: Prisma

State Management: React Context API (for simple, global state), Zustand (for complex, local state). Avoid Redux.

Development & Formatting Tools:

Linting/Formatting: ESLint and Prettier (enforced via pre-commit hooks).

Version Control: Git

API Client: Postman or Insomnia for backend testing.

3. Project Architecture & Folder Structure

This is the non-negotiable project structure.

/src
├── /assets        # Static, non-code assets (images, SVGs, fonts).
│
├── /components    # All React components.
│   ├── /sections  # Large, composite components that form a page section (e.g., HeroSection).
│   ├── /ui        # Atomic, highly reusable UI elements (Button, Card, Input). All shadcn/ui components reside here.
│   └── /           # Complex, shared components that are not page sections (e.g., Sidebar, InteractiveTerminal).
│
├── /constants     # Application-wide constants (e.g., API_URLS, siteMetadata).
│
├── /data          # Static content for the site (e.g., project details, skills list).
│
├── /hooks         # Custom React hooks (e.g., useScrollTracking).
│
├── /lib           # Utility functions, helper scripts, and library instances (e.g., utils.ts, axios.ts).
│
├── /pages         # Top-level page components that assemble sections.
│
├── /services      # API interaction layer. All fetch/axios calls and data transformation logic reside here.
│
├── /types         # Global TypeScript type definitions and interfaces.
│
├── App.tsx        # Main application component, handles routing and global providers.
├── main.tsx       # Application entry point.
└── index.css      # Global base styles and Tailwind directives.

4. Coding & Component Standards

Component Design:

Modularity: Every component must be modular and reusable where applicable.

Single Responsibility: A component should do one thing well. Decompose complex components into smaller, focused ones.

Props: Use descriptive TypeScript interface definitions for props. Always destructure props in the function signature. Avoid prop drilling through composition or state management.

Naming Conventions:

Components & Types: PascalCase (e.g., ProjectCard.tsx, type ProjectData).

Hooks: useCamelCase (e.g., useMobile.ts).

Variables & Functions: camelCase.

Constants: UPPER_SNAKE_CASE (e.g., API_BASE_URL).

Code Quality:

Imports: Organize imports in three groups: 1. External libraries, 2. Internal modules, 3. Styles/types.

API Logic: Abstract all API interactions into the /services directory. Components should not contain raw fetch or axios calls.

Error Handling: Use try/catch for all asynchronous operations. Implement graceful error states in the UI.

Cleanliness: No commented-out code, dead code, or console.log() statements in final commits.

5. Design, UI/UX, & Styling

Aesthetic: The design is clean, minimal, professional, modern, and premium. All new elements must strictly adhere to this aesthetic.

Styling: Use Tailwind CSS classes exclusively. Avoid custom CSS files unless absolutely necessary for a complex, non-replicable animation.

Layout: Use Flexbox and Grid for all layouts. The entire site must be fully responsive across mobile, tablet, and desktop viewports.

Consistency: Strictly reuse shadcn/ui components and theme values defined in tailwind.config.js (colors, spacing, fonts).

Accessibility (a11y): All components must be accessible. Use semantic HTML, provide alt tags for images, and use ARIA attributes where necessary.

Motion: Use animations from Framer Motion purposefully to enhance the user experience, guide attention, and provide feedback—not to distract.

6. Git & Version Control Workflow

Branching Strategy:

main: Production-ready, deployable code only.

develop: The primary development branch. All feature branches are merged here.

feature/feature-name: All new work must be done on a feature branch (e.g., feature/contact-form).

Commit Messages:

Follow the Conventional Commits specification (e.g., feat:, fix:, docs:, style:, refactor:, chore:).

Example: feat: add email validation to contact form

Pull Requests (PRs):

All code must be reviewed via a PR before merging into develop.

PRs must have a clear, descriptive title and a summary of changes.

7. Environment Variables

All sensitive keys, API URLs, and environment-specific configurations must be stored in .env files.

Frontend variables must be prefixed with VITE_ (e.g., VITE_SUPABASE_API_KEY).

Never commit .env files to version control. Provide a .env.example file with placeholder values.