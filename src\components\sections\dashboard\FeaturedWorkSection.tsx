import React, { useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useNavigate } from 'react-router-dom'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'
import { useScrollTracking } from '@/hooks/useScrollTracking'
import { featuredProjects, type Project } from '@/data/projects/projectsData'
import { OutlineButton } from '@/components/ui/enhanced-button'
import { scrollToTopInstant } from '@/components/ScrollToTop'

interface FeaturedWorkSectionProps {
  className?: string
}

export default function FeaturedWorkSection({ className }: FeaturedWorkSectionProps) {
  const { activeIndex, registerElement } = useScrollTracking(featuredProjects.length, {
    threshold: 0.6,
    rootMargin: '-10% 0px -10% 0px'
  })

  const navigate = useNavigate()
  const activeProject = featuredProjects[activeIndex]

  const handleNavigate = (path: string) => {
    scrollToTopInstant()
    navigate(path)
  }

  if (!activeProject) {
    return null
  }

  return (
    <section className={cn("mb-24 md:mb-32", className)}>
      {/* Section Header */}
      <div className="mb-12 text-center">
        <motion.h2
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-3xl md:text-4xl font-bold text-foreground tracking-tight mb-4"
        >
          Featured Work
        </motion.h2>
        <motion.p
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="text-lg text-muted-foreground max-w-2xl mx-auto leading-relaxed"
        >
          A curated selection of my best projects showcasing modern web development practices, innovative solutions, and user-centered design principles.
        </motion.p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-20 xl:gap-24 lg:-mt-48">

        {/* Left Column (Project Images) */}
        <div className="space-y-32 lg:space-y-40 lg:pt-64 lg:pb-64" id="projects-container">
          {featuredProjects.map((project, index) => (
            <ProjectImage
              key={project.id}
              project={project}
              index={index}
              isActive={index === activeIndex}
              onRegister={registerElement}
            />
          ))}
        </div>

        {/* Right Column (Sticky Details) */}
        <div className="relative">
          <div className="lg:sticky lg:top-0 lg:h-screen lg:flex lg:items-center">
            <div className="w-full">
              <AnimatePresence mode="wait">
                <motion.div
                  key={activeIndex}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.4, ease: "easeInOut" }}
                  className="space-y-8"
                >
                  {/* Project Content */}
                  <div className="space-y-4">
                    <h3 className="text-2xl lg:text-3xl font-bold text-foreground leading-tight">
                      {activeProject.title}
                    </h3>
                    <p className="text-muted-foreground leading-relaxed">
                      {activeProject.description}
                    </p>
                  </div>
                  <div className="space-y-4">
                    <div className="space-y-3">
                      {activeProject.features.slice(0, 4).map((feature, index) => (
                        <motion.div
                          key={index}
                          initial={{ opacity: 0, x: 10 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ duration: 0.3, delay: index * 0.1 }}
                          className="flex items-start gap-3"
                        >
                          <span className="text-primary font-bold text-sm mt-0.5">+</span>
                          <span className="text-sm text-muted-foreground leading-relaxed">
                            {feature}
                          </span>
                        </motion.div>
                      ))}
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div className="flex flex-wrap gap-2">
                      {activeProject.technologies.slice(0, 8).map((tech) => (
                        <Badge
                          key={tech}
                          variant="outline"
                          className="text-xs font-medium px-3 py-1.5 bg-background/50 hover:bg-primary/10 hover:text-primary hover:border-primary/50 transition-colors cursor-default"
                        >
                          {tech}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </motion.div>
              </AnimatePresence>
            </div>
          </div>
        </div>
      </div>

      <div className="text-center mt-20 lg:mt-8">
        <OutlineButton onClick={() => handleNavigate('/projects')}>
          See more projects
        </OutlineButton>
      </div>
    </section>
  )
}

// (No changes needed for the ProjectImage component)
interface ProjectImageProps {
  project: Project
  index: number
  isActive: boolean
  onRegister: (index: number, element: HTMLElement | null) => void
}

function ProjectImage({ project, index, isActive, onRegister }: ProjectImageProps) {
  const ref = useRef<HTMLDivElement>(null)

  useEffect(() => {
    onRegister(index, ref.current)
  }, [index, onRegister])

  return (
    <motion.div
      ref={ref}
      id={`project-${index}`}
      initial={{ opacity: 0, y: 50 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true, margin: "-100px" }}
      transition={{ duration: 0.8, delay: index * 0.1 }}
      className="relative group"
    >
      <Card className={cn(
        "overflow-hidden transition-all duration-700 border-2 shadow-sm",
        isActive
          ? "border-primary shadow-2xl shadow-primary/10 scale-[1.02] lg:scale-105"
          : "border-border/50 hover:border-border hover:shadow-lg group-hover:scale-[1.01]"
      )}>
        <CardContent className="p-0 relative">
          <div className="aspect-[4/3] relative overflow-hidden bg-gradient-to-br from-muted to-muted/50">
            <img
              src={project.image}
              alt={project.title}
              className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
            />
            <div className={cn(
              "absolute inset-0 transition-all duration-500",
              isActive
                ? "bg-gradient-to-t from-primary/20 via-transparent to-transparent"
                : "bg-gradient-to-t from-black/30 via-transparent to-transparent group-hover:from-black/20"
            )} />

            {/* Hover Overlay with Project Info */}
            <div className={cn(
              "absolute inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center transition-all duration-300",
              isActive ? "opacity-0" : "opacity-0 group-hover:opacity-100"
            )}>
              <div className="text-center text-white space-y-2 px-6">
                <h4 className="text-xl font-bold">{project.title}</h4>
                <p className="text-sm opacity-90">{project.highlight}</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}