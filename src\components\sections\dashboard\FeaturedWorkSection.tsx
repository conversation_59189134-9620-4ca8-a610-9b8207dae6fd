import React, { useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useNavigate } from 'react-router-dom'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ArrowRight, ExternalLink, GitBranch } from 'lucide-react'
import { cn } from '@/lib/utils'
import { useScrollTracking } from '@/hooks/useScrollTracking'
import { featuredProjects, type Project } from '@/data/projects/projectsData'
import { OutlineButton } from '@/components/ui/enhanced-button'
import { scrollToTopInstant } from '@/components/ScrollToTop'

interface FeaturedWorkSectionProps {
  className?: string
}

export default function FeaturedWorkSection({ className }: FeaturedWorkSectionProps) {
  const { activeIndex, registerElement } = useScrollTracking(featuredProjects.length, {
    threshold: 0.6,
    rootMargin: '-10% 0px -10% 0px'
  })

  const navigate = useNavigate()
  const activeProject = featuredProjects[activeIndex]

  const handleNavigate = (path: string) => {
    scrollToTopInstant()
    navigate(path)
  }

  if (!activeProject) {
    return null
  }

  return (
    <section className={cn("mb-24 md:mb-32 bg-background", className)}>
      {/* Section Header */}
      <div className="mb-16 text-center">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="mb-4"
        >
          <span className="text-sm font-medium text-primary tracking-wider uppercase">
            FEATURED CASE STUDIES
          </span>
        </motion.div>
        <motion.h2
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight mb-6"
        >
          Curated{' '}
          <span className="bg-gradient-to-r from-primary via-purple-500 to-pink-500 bg-clip-text text-transparent">
            work
          </span>
        </motion.h2>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-20 xl:gap-24">

        {/* Left Column (Project Cards) */}
        <div className="space-y-8 lg:space-y-12" id="projects-container">
          {featuredProjects.map((project, index) => (
            <ProjectCard
              key={project.id}
              project={project}
              index={index}
              isActive={index === activeIndex}
              onRegister={registerElement}
            />
          ))}
        </div>

        {/* Right Column (Project Details) */}
        <div className="lg:sticky lg:top-32 lg:h-fit">
          <AnimatePresence mode="wait">
            <ProjectDetails
              key={activeProject.id}
              project={activeProject}
              onNavigate={handleNavigate}
            />
          </AnimatePresence>
        </div>
      </div>

      <div className="text-center mt-20 lg:mt-8">
        <OutlineButton onClick={() => handleNavigate('/projects')}>
          See more projects
        </OutlineButton>
      </div>
    </section>
  )
}

// Project Card Component
interface ProjectCardProps {
  project: Project
  index: number
  isActive: boolean
  onRegister: (index: number, element: HTMLElement | null) => void
}

function ProjectCard({ project, index, isActive, onRegister }: ProjectCardProps) {
  const ref = useRef<HTMLDivElement>(null)

  useEffect(() => {
    onRegister(index, ref.current)
  }, [index, onRegister])

  // Get gradient colors based on project index
  const getGradientColors = (index: number) => {
    const gradients = [
      'from-purple-600 via-pink-600 to-purple-800', // Next Ventures
      'from-blue-600 via-indigo-600 to-blue-800',   // Zenith Minds
      'from-teal-600 via-green-600 to-teal-800',    // Snippix
    ]
    return gradients[index % gradients.length]
  }

  return (
    <motion.div
      ref={ref}
      id={`project-${index}`}
      initial={{ opacity: 0, y: 50 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true, margin: "-100px" }}
      transition={{ duration: 0.8, delay: index * 0.1 }}
      className="relative group"
    >
      <Card className={cn(
        "overflow-hidden transition-all duration-700 border-0 shadow-lg",
        "bg-gradient-to-br", getGradientColors(index),
        isActive
          ? "shadow-2xl shadow-primary/20 scale-[1.02] lg:scale-105"
          : "hover:shadow-xl group-hover:scale-[1.01]"
      )}>
        <CardContent className="p-8 relative">
          {/* Project Description */}
          <div className="mb-8">
            <p className="text-white/90 text-lg leading-relaxed">
              {project.description}
            </p>
            <div className="flex items-center mt-4">
              <ArrowRight className="w-5 h-5 text-white/70" />
            </div>
          </div>

          {/* Project Preview/Mockup */}
          <div className="relative">
            <div className="bg-black/20 backdrop-blur-sm rounded-lg p-4 border border-white/10">
              <img
                src={project.image}
                alt={project.title}
                className="w-full h-auto rounded-md transition-transform duration-700 group-hover:scale-[1.02]"
              />
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}

// Project Details Component
interface ProjectDetailsProps {
  project: Project
  onNavigate: (path: string) => void
}

function ProjectDetails({ project, onNavigate }: ProjectDetailsProps) {
  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      transition={{ duration: 0.4, ease: "easeInOut" }}
      className="space-y-8"
    >
      {/* Project Title */}
      <div className="space-y-4">
        <div className="flex items-center gap-3">
          <div className="w-3 h-3 bg-primary rounded-full"></div>
          <h3 className="text-2xl lg:text-3xl font-bold text-foreground leading-tight">
            {project.title}
          </h3>
        </div>
        <p className="text-muted-foreground leading-relaxed text-lg">
          {project.longDescription || project.description}
        </p>
      </div>

      {/* Features List */}
      <div className="space-y-4">
        <div className="space-y-3">
          {project.features.slice(0, 6).map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, x: 10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              className="flex items-start gap-3"
            >
              <span className="text-primary font-bold text-sm mt-1">+</span>
              <span className="text-sm text-muted-foreground leading-relaxed">
                {feature}
              </span>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Technologies */}
      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-3">
          {project.technologies.slice(0, 12).map((tech) => (
            <Badge
              key={tech}
              variant="outline"
              className="text-xs font-medium px-3 py-2 bg-background/50 hover:bg-primary/10 hover:text-primary hover:border-primary/50 transition-colors cursor-default justify-center"
            >
              {tech}
            </Badge>
          ))}
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex gap-4 pt-4">
        {project.liveUrl && project.liveUrl !== '#' && (
          <a
            href={project.liveUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center gap-2 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors text-sm font-medium"
          >
            <ExternalLink className="w-4 h-4" />
            Live Demo
          </a>
        )}
        {project.githubUrl && (
          <a
            href={project.githubUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center gap-2 px-4 py-2 border border-border rounded-lg hover:bg-muted transition-colors text-sm font-medium"
          >
            <GitBranch className="w-4 h-4" />
            Source Code
          </a>
        )}
      </div>
    </motion.div>
  )
}