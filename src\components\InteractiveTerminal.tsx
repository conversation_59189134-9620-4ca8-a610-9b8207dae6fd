// src/components/InteractiveTerminal.tsx

import React from 'react'
import Terminal from 'react-console-emulator'
import { motion } from 'framer-motion'

interface InteractiveTerminalProps {
  className?: string
}

export default function InteractiveTerminal({ className = '' }: InteractiveTerminalProps) {

  const getCurrentDate = () => {
    return new Date().toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    })
  }

  const commands = {
    help: {
      description: 'Show available commands',
      fn: () => (
        <div className="space-y-3">
          <p className="text-primary font-bold text-lg">📋 Available Commands:</p>
          <div className="grid grid-cols-1 gap-2 text-sm pl-4">
            <p><span className="text-primary font-mono">whois</span>    - Display information about me</p>
            <p><span className="text-primary font-mono">projects</span> - View my featured work</p>
            <p><span className="text-primary font-mono">skills</span>   - List my technical skills</p>
            <p><span className="text-primary font-mono">contact</span>  - Get my contact information</p>
            <p><span className="text-primary font-mono">resume</span>   - Download my resume</p>
            <p><span className="text-primary font-mono">neofetch</span> - Display system information</p>
            <p><span className="text-primary font-mono">clear</span>    - Clear the terminal</p>
          </div>
          <div className="border-t pt-2 mt-3 border-border">
            <p className="text-xs text-muted-foreground">💡 Tip: Try typing 'sudo' for a surprise!</p>
          </div>
        </div>
      )
    },

    commands: {
      description: 'Show available commands (alias for help)',
      fn: () => (
        <div className="space-y-3">
          <p className="text-primary font-bold text-lg">📋 Available Commands:</p>
          <div className="grid grid-cols-1 gap-2 text-sm pl-4">
            <p><span className="text-primary font-mono">help</span>     - Show this help message</p>
            <p><span className="text-primary font-mono">whois</span>    - Display information about me</p>
            <p><span className="text-primary font-mono">projects</span> - View my featured work</p>
            <p><span className="text-primary font-mono">skills</span>   - List my technical skills</p>
            <p><span className="text-primary font-mono">contact</span>  - Get my contact information</p>
            <p><span className="text-primary font-mono">resume</span>   - Download my resume</p>
            <p><span className="text-primary font-mono">neofetch</span> - Display system information</p>
            <p><span className="text-primary font-mono">clear</span>    - Clear the terminal</p>
          </div>
          <div className="border-t pt-2 mt-3 border-border">
            <p className="text-xs text-muted-foreground">💡 Tip: Try typing 'sudo' for a surprise!</p>
          </div>
        </div>
      )
    },

    whois: {
      description: 'Display information about me',
      fn: () => (
        <div className="space-y-3">
          <p className="text-primary font-bold">// CJ JUTBA - FULL-STACK DEVELOPER</p>
          <div className="space-y-2">
            <p>🎓 <strong>Education:</strong> Computer Engineering Graduate (May 2024)</p>
            <p>💻 <strong>Specialization:</strong> Modern web development with React & TypeScript</p>
            <p>🚀 <strong>Passion:</strong> Building responsive, user-friendly applications</p>
            <p>🎯 <strong>Goal:</strong> Turning complex problems into elegant solutions</p>
          </div>
          <p className="text-xs text-muted-foreground">
            Available for exciting frontend development opportunities.
          </p>
        </div>
      )
    },

    projects: {
      description: 'View my featured work',
      fn: () => (
        <div className="space-y-3">
          <p className="text-primary font-bold">Featured Projects:</p>
          <div className="space-y-2">
            <div>
              <p><span className="text-primary">01.</span> <strong>E-commerce Platform</strong></p>
              <p className="text-sm ml-4">Full-stack retail app with React, Next.js & TypeScript</p>
              <p className="text-sm ml-4 text-green-600 dark:text-green-400">Status: Completed ✓</p>
            </div>
            <div>
              <p><span className="text-primary">02.</span> <strong>Task Management App</strong></p>
              <p className="text-sm ml-4">Real-time collaborative workspace with Prisma & PostgreSQL</p>
              <p className="text-sm ml-4 text-blue-600 dark:text-blue-400">Status: In Progress ⚡</p>
            </div>
            <div>
              <p><span className="text-primary">03.</span> <strong>Portfolio Website</strong></p>
              <p className="text-sm ml-4">Modern portfolio with interactive terminal (you're using it!)</p>
              <p className="text-sm ml-4 text-green-600 dark:text-green-400">Status: Completed ✓</p>
            </div>
          </div>
          <p className="text-xs text-muted-foreground">
            💡 Type 'contact' to discuss potential collaborations!
          </p>
        </div>
      )
    },

    skills: {
      description: 'List my technical skills',
      fn: () => (
        <div className="space-y-3">
          <p className="text-primary font-bold">Technical Skills:</p>
          <div className="space-y-2">
            <div>
              <p className="text-primary">Frontend:</p>
              <p className="text-sm ml-4">React • TypeScript • Next.js • Tailwind CSS • JavaScript</p>
            </div>
            <div>
              <p className="text-primary">Backend:</p>
              <p className="text-sm ml-4">Node.js • Express • Prisma • PostgreSQL</p>
            </div>
            <div>
              <p className="text-primary">Tools & Others:</p>
              <p className="text-sm ml-4">Git • Vite • Supabase • Framer Motion • React Testing Library</p>
            </div>
          </div>
          <p className="text-xs text-muted-foreground">
            🌱 Currently learning: Advanced React patterns & performance optimization
          </p>
        </div>
      )
    },

    contact: {
      description: 'Get my contact information',
      fn: () => (
        <div className="space-y-3">
          <p className="text-primary font-bold">Let's Connect:</p>
          <div className="space-y-2">
            <p>📧 <strong>Email:</strong> <a href="mailto:<EMAIL>" className="text-primary hover:text-primary/80 transition-colors"><EMAIL></a></p>
            <p>💼 <strong>LinkedIn:</strong> <a href="https://linkedin.com/in/cjjutba" target="_blank" rel="noopener noreferrer" className="text-primary hover:text-primary/80 transition-colors">linkedin.com/in/cjjutba</a></p>
            <p>🐙 <strong>GitHub:</strong> <a href="https://github.com/cjjutba" target="_blank" rel="noopener noreferrer" className="text-primary hover:text-primary/80 transition-colors">github.com/cjjutba</a></p>
          </div>
          <p className="text-xs text-muted-foreground">
            Open to discussing new opportunities and exciting projects!
          </p>
        </div>
      )
    },

    resume: {
      description: 'Download my resume',
      fn: () => {
        // In a real implementation, you'd have an actual resume PDF
        window.open('/resume.pdf', '_blank')
        return (
          <div>
            <p className="text-primary">📄 Opening resume in new tab...</p>
            <p className="text-xs text-muted-foreground">
              If the download didn't start, please contact me directly.
            </p>
          </div>
        )
      }
    },

    neofetch: {
      description: 'Display system information',
      fn: () => (
        <div className="space-y-2">
          <p className="text-primary font-bold">cj@portfolio</p>
          <p>─────────────────</p>
          <p><span className="text-primary">OS:</span> Portfolio v2.0</p>
          <p><span className="text-primary">Host:</span> React + TypeScript</p>
          <p><span className="text-primary">Kernel:</span> Vite 5.4.19</p>
          <p><span className="text-primary">Shell:</span> Interactive Terminal</p>
          <p><span className="text-primary">Resolution:</span> Responsive Design</p>
          <p><span className="text-primary">Theme:</span> Purple Tech Professional</p>
          <p><span className="text-primary">CPU:</span> Passion-driven Development</p>
          <p><span className="text-orange-400">Memory:</span> Always Learning</p>
        </div>
      )
    },

    sudo: {
      description: 'Superuser access (Easter egg)',
      fn: () => (
        <div>
          <p className="text-red-600 dark:text-red-400">sudo: cj is not in the sudoers file. This incident will be reported.</p>
          <p className="text-xs text-muted-foreground">
            😄 Nice try! But I'm the only admin here.
          </p>
        </div>
      )
    },

    clear: {
      description: 'Clear the terminal screen',
      fn: (_args: any, _print: any, runCommand: any) => {
        setTimeout(() => {
          runCommand('clear')
        }, 100)
        return ''
      }
    }
  }

  const welcomeMessage = `
  Welcome to CJ's Interactive Portfolio Terminal
  Built with React, TypeScript, and Tailwind CSS
  Current date: ${getCurrentDate()}

  Type 'help' to see available commands or 'whois' to learn about me.
  `

  // Interactive Terminal for all devices
  return (
    <motion.div
      className={`w-full ${className}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8, delay: 0.3 }}
    >
      <div className="bg-card/95 backdrop-blur-sm border border-border/50 rounded-xl shadow-lg overflow-hidden">
        {/* Window Chrome */}
        <div className="flex items-center justify-between px-4 py-3 border-b bg-secondary/90 border-border/50">
          <div className="flex space-x-2">
            <div className="w-3 h-3 rounded-full bg-red-500 hover:bg-red-400 transition-colors cursor-pointer"></div>
            <div className="w-3 h-3 rounded-full bg-yellow-500 hover:bg-yellow-400 transition-colors cursor-pointer"></div>
            <div className="w-3 h-3 rounded-full bg-green-500 hover:bg-green-400 transition-colors cursor-pointer"></div>
          </div>
          <p className="text-sm font-mono text-muted-foreground">
            cj@portfolio: ~
          </p>
          <div className="w-12"></div>
        </div>

        {/* UPDATED: Reduced height from h-[500px] to h-[450px] */}
        <div className="h-[400px] w-full overflow-hidden">
          <div className="h-full overflow-y-auto font-mono">
            <Terminal
              commands={commands}
              welcomeMessage={welcomeMessage}
              promptLabel="<EMAIL>:~$"
              noDefaults={true}
              style={{
                backgroundColor: 'transparent',
                height: '100%',
                padding: '16px',
                fontSize: '14px',
                lineHeight: '1.5',
                fontFamily: 'JetBrains Mono, Fira Code, Monaco, Consolas, monospace',
                color: 'hsl(var(--foreground))'
              }}
              promptLabelStyle={{
                color: 'hsl(var(--primary))',
                fontWeight: 'bold'
              }}
              inputTextStyle={{
                color: 'hsl(var(--foreground))'
              }}
              messageStyle={{
                color: 'hsl(var(--foreground))'
              }}
              contentStyle={{
                color: 'hsl(var(--foreground))'
              }}
              commandStyle={{
                color: 'hsl(var(--foreground))'
              }}
            />
          </div>
        </div>
      </div>
    </motion.div>
  )
}