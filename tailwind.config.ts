import type { Config } from "tailwindcss";

export default {
	content: [
		"./pages/**/*.{ts,tsx}",
		"./components/**/*.{ts,tsx}",
		"./app/**/*.{ts,tsx}",
		"./src/**/*.{ts,tsx}",
	],
	prefix: "",
	theme: {
		container: {
			center: true,
			padding: '2rem',
			screens: {
				'2xl': '1400px'
			}
		},
		extend: {
			colors: {
				border: 'hsl(var(--border))',
				input: 'hsl(var(--input))',
				ring: 'hsl(var(--ring))',
				background: 'hsl(var(--background))',
				foreground: 'hsl(var(--foreground))',
				primary: {
					DEFAULT: 'hsl(var(--primary))',
					foreground: 'hsl(var(--primary-foreground))'
				},
				secondary: {
					DEFAULT: 'hsl(var(--secondary))',
					foreground: 'hsl(var(--secondary-foreground))'
				},
				destructive: {
					DEFAULT: 'hsl(var(--destructive))',
					foreground: 'hsl(var(--destructive-foreground))'
				},
				muted: {
					DEFAULT: 'hsl(var(--muted))',
					foreground: 'hsl(var(--muted-foreground))'
				},
				accent: {
					DEFAULT: 'hsl(var(--accent))',
					foreground: 'hsl(var(--accent-foreground))'
				},
				popover: {
					DEFAULT: 'hsl(var(--popover))',
					foreground: 'hsl(var(--popover-foreground))'
				},
				card: {
					DEFAULT: 'hsl(var(--card))',
					foreground: 'hsl(var(--card-foreground))'
				},
				portfolio: {
					accent: 'hsl(var(--portfolio-accent))',
					'accent-foreground': 'hsl(var(--portfolio-accent-foreground))',
					background: 'hsl(var(--portfolio-background))',
					surface: 'hsl(var(--portfolio-surface))',
					border: 'hsl(var(--portfolio-border))'
				},
				// Aayush Bharti Inspired Dark Theme Color Palette
				purple: {
					400: '#A78BFA', // Brand Purple Light
					500: '#9438D5', // Deep Purple/Magenta
					600: '#7C3AED', // Brand Purple Dark
				},
				violet: {
					400: '#A78BFA',
					500: '#9438D5',
					600: '#7C3AED',
				},
				pink: {
					500: '#DE55C8', // Vibrant Pink/Magenta
					600: '#EEA2C8', // Softer Pink
				},
				orange: {
					500: '#FF6B6B', // Vibrant Orange/Red for gradients
				},
				teal: {
					500: '#2EB29C', // Strong Green/Teal
				},
				blue: {
					500: '#2D66E0', // Prominent Blue
				},
				emerald: {
					500: '#10B981', // Success/Available
				},
				amber: {
					500: '#F59E0B', // Warning
				},
				zinc: {
					50: '#FFFFFF',   // Pure white for primary text
					400: '#AAAAAA',  // Light grey for secondary text
					500: '#71717A',  // Medium grey
					900: '#121212',  // Deep dark grey/near black
				}
			},
			backgroundImage: {
				'gradient-primary': 'var(--gradient-primary)',
				'gradient-subtle': 'var(--gradient-subtle)'
			},
			boxShadow: {
				'soft': 'var(--shadow-soft)',
				'medium': 'var(--shadow-medium)',
				'large': 'var(--shadow-large)'
			},
			borderRadius: {
				lg: 'var(--radius)',
				md: 'calc(var(--radius) - 2px)',
				sm: 'calc(var(--radius) - 4px)'
			},
			keyframes: {
				'accordion-down': {
					from: {
						height: '0'
					},
					to: {
						height: 'var(--radix-accordion-content-height)'
					}
				},
				'accordion-up': {
					from: {
						height: 'var(--radix-accordion-content-height)'
					},
					to: {
						height: '0'
					}
				}
			},
			animation: {
				'accordion-down': 'accordion-down 0.2s ease-out',
				'accordion-up': 'accordion-up 0.2s ease-out'
			}
		}
	},
	plugins: [require("tailwindcss-animate")],
} satisfies Config;
