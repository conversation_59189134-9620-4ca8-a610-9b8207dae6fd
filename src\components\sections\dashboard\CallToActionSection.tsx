import React from 'react'
import { useNavigate } from 'react-router-dom'
import { Card, CardContent } from '@/components/ui/card'
import Section from '@/components/Section'
import { scrollToTopInstant } from '@/components/ScrollToTop'
import { PrimaryButton, OutlineButton } from '@/components/ui/enhanced-button'
import { BaseSectionProps } from './types'
import { createNavigationHandler } from './utils'

export default function CallToActionSection({ className }: BaseSectionProps) {
  const navigate = useNavigate()
  const handleNavigation = createNavigationHandler(navigate, scrollToTopInstant)

  return (
    <Section animate={false} className={className}>
      <div className="relative">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-primary/10 dark:from-primary/10 dark:to-primary/20 rounded-2xl"></div>
        <Card className="relative border-border/50 bg-card/90 backdrop-blur-sm hover:shadow-sm transition-all duration-200">
          <CardContent className="p-12 text-center">
            <div className="space-y-8">
              <div className="space-y-4">
                <h2 className="text-3xl md:text-4xl lg:text-5xl font-extrabold text-foreground tracking-tight">
                  Have a project in mind?
                </h2>
                <p className="text-lg md:text-xl text-muted-foreground max-w-2xl mx-auto leading-relaxed mt-6">
                  I'm always open to discussing new projects and opportunities.
                  Let's create something amazing together.
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-6 justify-center items-center pt-6">
                <PrimaryButton
                  onClick={() => handleNavigation('/contact')}
                  className="px-6 py-4"
                  arrowAnimation="slide"
                >
                  Contact Me
                </PrimaryButton>
                <OutlineButton
                  onClick={() => handleNavigation('/about')}
                  className="px-6 py-4"
                  arrowAnimation="fade"
                >
                  Learn More About Me
                </OutlineButton>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </Section>
  )
}
