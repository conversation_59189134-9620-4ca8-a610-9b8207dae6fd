import React from 'react'
import { motion } from 'framer-motion'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Clock, BookOpen } from 'lucide-react'
import Section from '@/components/Section'
import { BaseSectionProps, staggerContainerVariants, itemVariants } from './types'
import { currentlyLearning } from '@/data/skills/skillsData'
import { renderIcon, getPriorityColor } from './utils'

export default function LearningGoalsSection({ className }: BaseSectionProps) {
  return (
    <Section
      title="Currently Learning"
      subtitle="My ongoing learning journey and the technologies I'm actively exploring to stay ahead of the curve."
      className={className}
    >
      <motion.div
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
        variants={staggerContainerVariants}
      >
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {currentlyLearning.map((goal, index) => (
            <motion.div key={goal.name} variants={itemVariants}>
              <Card className="group h-full hover:shadow-lg transition-all duration-300 border-border/50 bg-card/90 backdrop-blur-sm transform hover:scale-[1.02]">
                <CardHeader className="pb-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 rounded-lg bg-background/50 flex items-center justify-center">
                        {renderIcon(goal.icon, "w-5 h-5 text-primary")}
                      </div>
                      <div>
                        <CardTitle className="text-lg font-bold group-hover:text-primary transition-colors duration-300">
                          {goal.name}
                        </CardTitle>
                      </div>
                    </div>
                    <Badge
                      variant="secondary"
                      className={getPriorityColor(goal.priority)}
                    >
                      {goal.priority}
                    </Badge>
                  </div>
                  
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Progress</span>
                      <Badge variant="outline" className="font-bold text-primary">
                        {goal.progress}%
                      </Badge>
                    </div>
                    <Progress value={goal.progress} className="h-2" />
                  </div>
                </CardHeader>
                
                <CardContent className="space-y-4">
                  <CardDescription className="text-sm leading-relaxed">
                    {goal.description}
                  </CardDescription>
                  
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <Clock className="w-4 h-4" />
                      <span>{goal.timeline}</span>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <BookOpen className="w-4 h-4 text-primary" />
                      <span className="text-sm font-medium text-primary">Learning Resources:</span>
                    </div>
                    <div className="flex flex-wrap gap-1">
                      {goal.resources.map((resource, idx) => (
                        <Badge
                          key={idx}
                          variant="outline"
                          className="text-xs px-2 py-1"
                        >
                          {resource}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </motion.div>
    </Section>
  )
}
