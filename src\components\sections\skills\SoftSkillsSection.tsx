import React from 'react'
import { motion } from 'framer-motion'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import Section from '@/components/Section'
import { BaseSectionProps, staggerContainerVariants, itemVariants } from './types'
import { professionalSkills as softSkills } from '@/data/skills/skillsData'

export default function SoftSkillsSection({ className }: BaseSectionProps) {
  const getStrengthColor = (strength: number) => {
    if (strength >= 90) return "text-green-600 dark:text-green-400"
    if (strength >= 80) return "text-blue-600 dark:text-blue-400"
    if (strength >= 70) return "text-yellow-600 dark:text-yellow-400"
    return "text-orange-600 dark:text-orange-400"
  }

  const getStrengthBg = (strength: number) => {
    if (strength >= 90) return "bg-green-500/10 border-green-500/20"
    if (strength >= 80) return "bg-blue-500/10 border-blue-500/20"
    if (strength >= 70) return "bg-yellow-500/10 border-yellow-500/20"
    return "bg-orange-500/10 border-orange-500/20"
  }

  return (
    <Section
      title="Professional Skills"
      subtitle="The soft skills and professional competencies that complement my technical expertise."
      className={className}
    >
      <motion.div
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
        variants={staggerContainerVariants}
      >
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {softSkills.map((skill, index) => (
            <motion.div key={skill.name} variants={itemVariants}>
              <Card className={`group h-full hover:shadow-lg transition-all duration-300 border-border/50 bg-card/90 backdrop-blur-sm transform hover:scale-[1.02] ${getStrengthBg(skill.strength)}`}>
                <CardHeader className="pb-4">
                  <div className="flex items-center gap-3 mb-2">
                    <div className="w-10 h-10 rounded-lg bg-background/50 flex items-center justify-center">
                      <skill.icon className="w-5 h-5 text-primary" />
                    </div>
                    <div className="flex-1">
                      <CardTitle className="text-lg font-bold group-hover:text-primary transition-colors duration-300">
                        {skill.name}
                      </CardTitle>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <Progress value={skill.strength} className="flex-1 h-2" />
                    <Badge
                      variant="secondary"
                      className={`ml-3 font-bold ${getStrengthColor(skill.strength)}`}
                    >
                      {skill.strength}%
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-sm leading-relaxed mb-4">
                    {skill.description}
                  </CardDescription>
                  <div className="space-y-2">
                    <p className="text-xs font-medium text-primary">Key Applications:</p>
                    <div className="flex flex-wrap gap-1">
                      {skill.examples.map((example, idx) => (
                        <Badge
                          key={idx}
                          variant="outline"
                          className="text-xs px-2 py-1"
                        >
                          {example}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </motion.div>
    </Section>
  )
}
