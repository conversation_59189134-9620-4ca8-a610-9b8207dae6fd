import React from 'react'
import { motion } from 'framer-motion'
import { useNavigate } from 'react-router-dom'
import { PrimaryButton, OutlineButton } from '@/components/ui/enhanced-button'
import { scrollToTopInstant } from '@/components/ScrollToTop'
import { BaseSectionProps, containerVariants, itemVariants } from './types'
import { createNavigationHandler } from './utils'

export default function HeroSection({ className }: BaseSectionProps) {
  const navigate = useNavigate()
  const handleNavigation = createNavigationHandler(navigate, scrollToTopInstant)

  return (
    <motion.div
      className={`mb-24 md:mb-32 ${className || ''}`}
      initial="hidden"
      animate="visible"
      variants={containerVariants}
    >
      <div className="text-center space-y-8">
        <motion.div
          className="space-y-6"
          variants={itemVariants}
        >
          <motion.h1
            className="text-4xl md:text-5xl lg:text-6xl font-extrabold text-foreground leading-tight tracking-tight"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, delay: 0.05 }}
          >
            Skills &{' '}
            <span className="text-primary bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">
              Expertise
            </span>
          </motion.h1>

          <motion.p
            className="text-lg md:text-xl text-muted-foreground leading-relaxed max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, delay: 0.1 }}
          >
            A comprehensive showcase of my technical abilities, professional skills, and continuous learning journey.
            From frontend mastery to emerging technologies, here's what I bring to every project.
          </motion.p>
        </motion.div>

        <motion.div
          className="flex flex-col sm:flex-row gap-4 justify-center items-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.15 }}
        >
          <PrimaryButton
            onClick={() => handleNavigation('/projects')}
            className="px-8 py-4"
            arrowAnimation="slide"
          >
            See Skills in Action
          </PrimaryButton>
          <OutlineButton
            onClick={() => handleNavigation('/contact')}
            className="px-8 py-4"
            arrowAnimation="fade"
          >
            Let's Work Together
          </OutlineButton>
        </motion.div>
      </div>
    </motion.div>
  )
}
