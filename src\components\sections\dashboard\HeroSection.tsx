// src/components/sections/HeroSection.tsx

import React from 'react'
import { motion } from 'framer-motion'
import { useNavigate } from 'react-router-dom'
import InteractiveTerminal from '@/components/InteractiveTerminal'
import { scrollToTopInstant } from '@/components/ScrollToTop'
import { PrimaryButton } from '@/components/ui/enhanced-button'
import { BaseSectionProps, containerVariants } from './types'
import { createNavigationHandler } from './utils'
import { userData } from '@/data/personal/userData'

export default function HeroSection({ className }: BaseSectionProps) {
  const navigate = useNavigate()
  const handleNavigation = createNavigationHandler(navigate, scrollToTopInstant)

  return (
    // UPDATED: Added pt-16 md:pt-24 for top padding
    <motion.div
      className={`pt-12 md:pt-18 mb-24 md:mb-32 ${className || ''}`}
      initial="hidden"
      animate="visible"
      variants={containerVariants}
    >
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center">
        {/* Left Column - Content */}
        <div className="space-y-8 order-1 lg:order-1">
          <div className="space-y-6">
            <motion.h1
              className="text-4xl md:text-5xl lg:text-6xl font-bold text-foreground leading-tight tracking-tight flex flex-col"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              {/* First line */}
              <motion.span
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                I build web apps
              </motion.span>

              {/* Second line (highlighted) */}
              <motion.span
                className="text-primary bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.4 }}
              >
                that work
              </motion.span>
            </motion.h1>

            <motion.p
              className="text-lg md:text-xl text-slate-600 dark:text-slate-300 leading-relaxed"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              {userData.bios.medium}
            </motion.p>

            {/* Integrated terminal prompt - cleaner design */}
            <motion.p
              className="text-sm text-muted-foreground"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.9 }}
            >
              → Try the interactive terminal on the right! Type <code className="bg-muted px-1.5 py-0.5 rounded text-foreground font-mono text-xs">help</code> to get started
            </motion.p>
          </div>

          <motion.div
            className="flex flex-col sm:flex-row gap-4 pt-4"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.0 }}
          >
            <PrimaryButton
              onClick={() => handleNavigation('/projects')}
              arrowAnimation="slide"
              className="px-6 py-3"
            >
              View My Work
            </PrimaryButton>
            <button
              type="button"
              onClick={() => handleNavigation('/about')}
              className="inline-flex items-center gap-2 text-sm font-medium text-muted-foreground hover:text-foreground transition-colors duration-200 group"
            >
              About My Journey
              <motion.span
                className="opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                initial={{ x: -4 }}
                whileHover={{ x: 0 }}
              >
                →
              </motion.span>
            </button>
          </motion.div>
        </div>

        {/* Right Column - Interactive Terminal */}
        <div className="order-2 lg:order-2">
          <InteractiveTerminal />
        </div>
      </div>
    </motion.div>
  )
}