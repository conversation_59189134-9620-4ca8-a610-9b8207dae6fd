import React from 'react'
import { motion } from 'framer-motion'
import { useNavigate } from 'react-router-dom'
import { GraduationCap, Rocket } from 'lucide-react'
import { Card, CardContent } from '@/components/ui/card'
import Section from '@/components/Section'
import { scrollToTopInstant } from '@/components/ScrollToTop'
import { OutlineButton } from '@/components/ui/enhanced-button'
import { BaseSectionProps, itemVariants } from './types'
import { createNavigationHandler } from './utils'

export default function JourneySummarySection({ className }: BaseSectionProps) {
  const navigate = useNavigate()
  const handleNavigation = createNavigationHandler(navigate, scrollToTopInstant)

  return (
    <Section
      title="My Journey"
      subtitle="From Computer Engineering graduate to passionate Frontend Developer."
      className={className}
    >
      <motion.div
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
        variants={itemVariants}
      >
        <Card className="border-border/50 bg-card/90 backdrop-blur-sm hover:shadow-sm transition-all duration-200">
          <CardContent className="p-8 text-center">
            <div className="space-y-6">
              <div className="flex items-center justify-center space-x-4">
                <div className="flex items-center space-x-2">
                  <GraduationCap className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                  <span className="text-sm font-medium text-muted-foreground">May 2025</span>
                </div>
                <div className="w-8 h-[2px] bg-gradient-to-r from-purple-600 to-primary"></div>
                <div className="flex items-center space-x-2">
                  <Rocket className="w-5 h-5 text-primary" />
                  <span className="text-sm font-medium text-primary">Present</span>
                </div>
              </div>

              <p className="text-lg text-muted-foreground leading-relaxed max-w-2xl mx-auto">
                After graduating with a Computer Engineering degree in May 2025, I discovered my passion for frontend
                development. I'm committed to continuous learning and building applications that provide exceptional user experiences.
              </p>

              <OutlineButton
                onClick={() => handleNavigation('/about')}
              >
                See my full journey
              </OutlineButton>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </Section>
  )
}
