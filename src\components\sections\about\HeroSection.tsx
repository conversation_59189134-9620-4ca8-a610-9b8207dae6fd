import React from 'react'
import { motion } from 'framer-motion'
import { useNavigate } from 'react-router-dom'
import { PrimaryButton, OutlineButton } from '@/components/ui/enhanced-button'
import { scrollToTopInstant } from '@/components/ScrollToTop'
import { BaseSectionProps, containerVariants } from './types'
import { createNavigationHandler } from './utils'
import { userData } from '@/data/personal/userData'
import { enhancedContent } from '@/data/personal/enhancedContent'

export default function HeroSection({ className }: BaseSectionProps) {
  const navigate = useNavigate()
  const handleNavigation = createNavigationHandler(navigate, scrollToTopInstant)

  return (
    <motion.div
      className={`mb-24 md:mb-32 ${className || ''}`}
      initial="hidden"
      animate="visible"
      variants={containerVariants}
    >
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center">
        {/* Left Column - Content */}
        <div className="space-y-8 order-1 lg:order-1">
          <div className="space-y-6">
            <motion.h1
              className="text-4xl md:text-5xl lg:text-6xl font-extrabold text-foreground leading-tight tracking-tight"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: 0.05 }}
            >
              {enhancedContent.about.title.split(' ').slice(0, -2).join(' ')}{' '}
              <span className="text-primary">
                End-to-End Development
              </span>
            </motion.h1>

            <motion.p
              className="text-lg md:text-xl text-muted-foreground leading-relaxed max-w-2xl"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: 0.1 }}
            >
              I'm {userData.name}, a {userData.education.degree} graduate who discovered that true value lies in controlling the entire lifecycle of an application. I combine systematic engineering principles with creative problem-solving to architect, build, and deploy complete solutions from database to user interface.
            </motion.p>
          </div>

          <motion.div
            className="flex flex-col sm:flex-row gap-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, delay: 0.15 }}
          >
            <PrimaryButton
              onClick={() => handleNavigation('/contact')}
              className="px-8 py-3"
              arrowAnimation="slide"
            >
              Let's Connect
            </PrimaryButton>
            <OutlineButton
              onClick={() => handleNavigation('/projects')}
              className="px-8 py-3"
            >
              View My Work
            </OutlineButton>
          </motion.div>
        </div>

        {/* Right Column - Profile Image */}
        <motion.div
          className="order-2 lg:order-2 flex justify-center lg:justify-end"
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.4, delay: 0.1 }}
        >
          <div className="relative w-72 h-72 md:w-80 md:h-80 lg:w-96 lg:h-96">
            <img
              src="/about_profile.jpg"
              alt="CJ Jutba"
              className="w-full h-full object-cover rounded-2xl border border-border shadow-lg"
            />
          </div>
        </motion.div>
      </div>
    </motion.div>
  )
}
