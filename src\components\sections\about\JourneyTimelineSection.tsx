import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Lightbulb, ExternalLink } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { OutlineButton } from '@/components/ui/enhanced-button'
import { cn } from '@/lib/utils'
import Section from '@/components/Section'
import { BaseSectionProps, staggerContainerVariants, itemVariants } from './types'
import { timelineData } from './data'

export default function JourneyTimelineSection({ className }: BaseSectionProps) {
  const [activeTimelineItem, setActiveTimelineItem] = useState<number | null>(null)

  return (
    <Section
      title="My Journey"
      subtitle="From Computer Engineering graduate to Full-Stack Developer - discovering that true value lies in building complete solutions from database to deployment."
      className={className}
    >
      <motion.div
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
        variants={staggerContainerVariants}
      >
        <div className="relative">
          {/* Timeline Line */}
          <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-gradient-to-b from-primary/50 via-primary/30 to-transparent"></div>

          <div className="space-y-8">
            {timelineData.map((item, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                className="relative flex items-start gap-6"
              >
                {/* Timeline Dot */}
                <div className="relative z-10 flex-shrink-0">
                  <div className={cn(
                    "w-16 h-16 rounded-full border-4 border-background flex items-center justify-center transition-all duration-300",
                    activeTimelineItem === index
                      ? "bg-primary text-primary-foreground shadow-lg scale-110"
                      : "bg-card hover:bg-primary/10 text-primary shadow-md hover:scale-105"
                  )}>
                    <item.icon className="w-6 h-6" />
                  </div>
                </div>

                {/* Timeline Content */}
                <Card
                  className={cn(
                    "flex-1 cursor-pointer transition-all duration-300 border-border/50 bg-card/90 backdrop-blur-sm hover:shadow-lg transform hover:scale-[1.02]",
                    activeTimelineItem === index && "ring-2 ring-primary/20 shadow-xl"
                  )}
                  onClick={() => setActiveTimelineItem(activeTimelineItem === index ? null : index)}
                >
                  <CardHeader className="pb-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle className="text-xl font-bold">{item.title}</CardTitle>
                        <CardDescription className="text-primary font-medium mt-1">
                          {item.date}
                        </CardDescription>
                      </div>
                      <Badge variant="secondary" className="bg-primary/10 text-primary border-primary/20">
                        {item.category}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground leading-relaxed mb-4">
                      {item.description}
                    </p>

                    <AnimatePresence>
                      {activeTimelineItem === index && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: "auto" }}
                          exit={{ opacity: 0, height: 0 }}
                          transition={{ duration: 0.3 }}
                          className="border-t border-border/50 pt-4 mt-4"
                        >
                          <div className="space-y-3">
                            <div className="flex items-center gap-2 text-sm font-medium text-primary">
                              <Lightbulb className="w-4 h-4" />
                              Key Learning
                            </div>
                            <p className="text-sm text-muted-foreground italic">
                              "{item.keyLearning}"
                            </p>
                            {item.link && (
                              <OutlineButton
                                className="mt-3 text-sm px-4 py-2"
                                onClick={(e) => {
                                  e.stopPropagation()
                                  window.open(item.link, '_blank')
                                }}
                                showArrow={false}
                              >
                                <ExternalLink className="w-3 h-3 mr-2" />
                                View Project
                              </OutlineButton>
                            )}
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </motion.div>
    </Section>
  )
}
